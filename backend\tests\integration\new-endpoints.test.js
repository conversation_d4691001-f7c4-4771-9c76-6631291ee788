/**
 * Integration Tests for New Endpoints
 * 
 * This test suite validates all newly implemented endpoints to ensure they work correctly
 * with proper authentication, validation, and error handling.
 */

const request = require('supertest');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Test configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'admin123'
};

let authToken = null;
let testPropertyId = null;
let testUserId = null;

describe('New Endpoints Integration Tests', () => {
  beforeAll(async () => {
    // Setup test data
    await setupTestData();
    
    // Authenticate
    const loginResponse = await request(BASE_URL)
      .post('/api/auth/login')
      .send(TEST_USER);
    
    if (loginResponse.status === 200 && loginResponse.body.data?.token) {
      authToken = loginResponse.body.data.token;
    }
  });

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
    await prisma.$disconnect();
  });

  describe('Authentication Endpoints', () => {
    test('POST /api/auth/logout - should logout user successfully', async () => {
      const response = await request(BASE_URL)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.message).toContain('Logout successful');
    });
  });

  describe('Notification Endpoints', () => {
    test('PATCH /api/notifications/mark-all-read - should mark all notifications as read', async () => {
      // First create some test notifications
      await createTestNotifications();

      const response = await request(BASE_URL)
        .patch('/api/notifications/mark-all-read')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('count');
    });

    test('GET /api/notifications/sse - should establish SSE connection', async () => {
      const response = await request(BASE_URL)
        .get('/api/notifications/sse')
        .set('Authorization', `Bearer ${authToken}`)
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toContain('text/event-stream');
    });
  });

  describe('Bulk Operations', () => {
    test('POST /api/thresholds/bulk - should create multiple thresholds', async () => {
      const testThresholds = [
        {
          functional_area: 'fuel',
          property_type: 'residential',
          metric_name: 'fuel_level_test',
          threshold_type: 'min',
          min_value: 20,
          unit: 'liters',
          severity: 'medium',
        },
        {
          functional_area: 'attendance',
          property_type: 'office',
          metric_name: 'attendance_rate_test',
          threshold_type: 'min',
          min_value: 80,
          unit: 'percentage',
          severity: 'high',
        },
      ];

      const response = await request(BASE_URL)
        .post('/api/thresholds/bulk')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ thresholds: testThresholds });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.created).toHaveLength(2);
      expect(response.body.data.errors).toHaveLength(0);
    });

    test('POST /api/users/bulk-approve - should validate input correctly', async () => {
      // Test with empty array (should fail validation)
      const response = await request(BASE_URL)
        .post('/api/users/bulk-approve')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ user_ids: [] });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('POST /api/attendance/bulk - should process attendance records', async () => {
      const testRecords = [
        {
          user_id: testUserId,
          property_id: testPropertyId,
          date: new Date().toISOString().split('T')[0],
          status: 'present',
          hours_worked: 8,
          notes: 'Test attendance record',
        },
      ];

      const response = await request(BASE_URL)
        .post('/api/attendance/bulk')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ records: testRecords });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.summary.total).toBe(1);
    });
  });

  describe('Monitoring Endpoints', () => {
    test('POST /api/monitoring - should submit single metric', async () => {
      const testMetric = {
        property_id: testPropertyId,
        service_type: 'fuel',
        metric_name: 'fuel_level',
        value: 75.5,
        unit: 'liters',
      };

      const response = await request(BASE_URL)
        .post('/api/monitoring')
        .set('Authorization', `Bearer ${authToken}`)
        .send(testMetric);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
    });

    test('POST /api/monitoring/multiple - should submit multiple metrics', async () => {
      const testData = {
        property_id: testPropertyId,
        metrics: [
          {
            service_type: 'fuel',
            metric_name: 'fuel_level',
            value: 80.0,
            unit: 'liters',
          },
          {
            service_type: 'electricity',
            metric_name: 'power_status',
            value: 1,
            unit: 'boolean',
          },
        ],
      };

      const response = await request(BASE_URL)
        .post('/api/monitoring/multiple')
        .set('Authorization', `Bearer ${authToken}`)
        .send(testData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
    });
  });

  describe('Permission Endpoints', () => {
    test('GET /api/permissions/screens - should get screen permissions', async () => {
      const response = await request(BASE_URL)
        .get('/api/permissions/screens')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    test('GET /api/permissions/widgets - should get widget permissions', async () => {
      const response = await request(BASE_URL)
        .get('/api/permissions/widgets')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });
});

// Helper functions
async function setupTestData() {
  try {
    // Create test property
    const property = await prisma.property.create({
      data: {
        name: 'Test Property for New Endpoints',
        type: 'residential',
        address: 'Test Address',
        isActive: true,
      },
    });
    testPropertyId = property.id;

    // Get test user
    const user = await prisma.user.findFirst({
      where: { email: TEST_USER.email },
    });
    if (user) {
      testUserId = user.id;
    }
  } catch (error) {
    console.error('Setup test data error:', error);
  }
}

async function cleanupTestData() {
  try {
    // Clean up test data
    if (testPropertyId) {
      await prisma.attendance.deleteMany({
        where: { propertyId: testPropertyId },
      });
      
      await prisma.property.delete({
        where: { id: testPropertyId },
      });
    }

    // Clean up test thresholds
    await prisma.thresholdConfig.deleteMany({
      where: {
        metricName: {
          in: ['fuel_level_test', 'attendance_rate_test'],
        },
      },
    });
  } catch (error) {
    console.error('Cleanup test data error:', error);
  }
}

async function createTestNotifications() {
  try {
    if (testUserId) {
      await prisma.notification.createMany({
        data: [
          {
            userId: testUserId,
            title: 'Test Notification 1',
            message: 'This is a test notification',
            type: 'system',
            priority: 'medium',
            isRead: false,
          },
          {
            userId: testUserId,
            title: 'Test Notification 2',
            message: 'This is another test notification',
            type: 'user',
            priority: 'low',
            isRead: false,
          },
        ],
      });
    }
  } catch (error) {
    console.error('Create test notifications error:', error);
  }
}
